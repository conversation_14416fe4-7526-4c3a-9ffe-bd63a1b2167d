<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo esc_html($page_title); ?> - <?php bloginfo('name'); ?></title>
    
    <!-- تحميل أنماط ووردبريس -->
    <?php wp_head(); ?>
    
    <style>
        body {
            color: <?php echo esc_attr($settings['text_color']); ?>;
        }
        
        .ridcod-countdown-section {
            text-align: center;
            padding: 30px 20px;
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .ridcod-countdown-timer {
            font-size: 48px;
            font-weight: bold;
            margin: 20px 0;
            color: <?php echo esc_attr($settings['countdown_color']); ?>;
        }
        
        .ridcod-countdown-message {
            font-size: 18px;
            margin-bottom: 20px;
            color: <?php echo esc_attr($settings['text_color']); ?>;
        }
        
        .ridcod-continue-btn {
            background: <?php echo esc_attr($settings['button_color']); ?>;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 16px;
            border-radius: 3px;
            cursor: pointer;
            display: none;
        }
        
        .ridcod-continue-btn:hover {
            opacity: 0.8;
        }
        
        .ridcod-redirect-section {
            text-align: center;
            padding: 30px;
            margin-top: 50px;
            border: 1px solid #ddd;
            border-radius: 5px;
            display: none;
        }
        
        .ridcod-redirect-btn {
            background: #0073aa;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
        
        .ridcod-redirect-btn:hover {
            background: #005a87;
            color: white;
            text-decoration: none;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        @media (max-width: 768px) {
            .ridcod-countdown-timer {
                font-size: 36px;
            }
            
            .ridcod-countdown-message {
                font-size: 16px;
            }
            
            .ridcod-continue-btn,
            .ridcod-redirect-btn {
                width: 100%;
                padding: 15px;
            }
        }
    </style>
</head>
<body <?php body_class(); ?>>

<?php
// استخدام header ووردبريس إذا كان متاحاً
if (function_exists('get_header') && !headers_sent()) {
    get_header();
}
?>

<main class="ridcod-intermediate-page">
    <div class="container">
        
        <!-- قسم العداد التنازلي -->
        <div id="ridcod-countdown-section" class="ridcod-countdown-section">
            <div class="ridcod-countdown-message">
                <?php echo esc_html($settings['countdown_text']); ?>
            </div>
            <div id="ridcod-countdown-timer" class="ridcod-countdown-timer">
                <?php echo $short_url->countdown_seconds; ?>
            </div>
            <button id="ridcod-continue-btn" class="ridcod-continue-btn">
                <?php echo esc_html($settings['continue_text']); ?>
            </button>
        </div>
        
        <!-- محتوى الصفحة -->
        <div class="ridcod-page-content">
            <article class="page-content">
                <header class="entry-header">
                    <h1 class="entry-title"><?php echo esc_html($page_title); ?></h1>
                </header>
                
                <div class="entry-content">
                    <?php echo $page_content; ?>
                </div>
            </article>
        </div>
        
        <!-- قسم الانتقال إلى الرابط -->
        <div id="ridcod-redirect-section" class="ridcod-redirect-section">
            <h3><?php echo esc_html($settings['button_text']); ?></h3>
            <p><?php echo esc_html($settings['redirect_text']); ?></p>
            <a href="<?php echo esc_url($short_url->original_url); ?>" 
               class="ridcod-redirect-btn" 
               id="ridcod-redirect-btn"
               style="background: <?php echo esc_attr($settings['button_color']); ?>;">
                <?php echo esc_html($settings['button_text']); ?>
            </a>
        </div>
        
    </div>
</main>

<?php
// استخدام footer ووردبريس إذا كان متاحاً
if (function_exists('get_footer') && !headers_sent()) {
    get_footer();
}
?>

<script>
(function() {
    let countdown = <?php echo $short_url->countdown_seconds; ?>;
    const timerElement = document.getElementById('ridcod-countdown-timer');
    const continueBtn = document.getElementById('ridcod-continue-btn');
    const countdownSection = document.getElementById('ridcod-countdown-section');
    const redirectSection = document.getElementById('ridcod-redirect-section');
    
    // بدء العداد التنازلي
    const timer = setInterval(() => {
        countdown--;
        timerElement.textContent = countdown;
        
        if (countdown <= 0) {
            clearInterval(timer);
            timerElement.textContent = '0';
            continueBtn.style.display = 'inline-block';
        }
    }, 1000);
    
    // عند النقر على زر المتابعة
    continueBtn.addEventListener('click', function() {
        // إخفاء قسم العداد
        countdownSection.style.display = 'none';
        
        // التمرير إلى أسفل الصفحة
        setTimeout(() => {
            window.scrollTo({
                top: document.body.scrollHeight,
                behavior: 'smooth'
            });
            
            // إظهار قسم الانتقال
            setTimeout(() => {
                redirectSection.style.display = 'block';
            }, 500);
        }, 300);
    });
    
    // منع النقر بالزر الأيمن والاختصارات
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });
    
    document.addEventListener('keydown', function(e) {
        if (e.key === 'F12' || 
            (e.ctrlKey && e.shiftKey && e.key === 'I') ||
            (e.ctrlKey && e.key === 'u') ||
            (e.ctrlKey && e.key === 's')) {
            e.preventDefault();
        }
    });
    
})();
</script>

<?php wp_footer(); ?>
</body>
</html>
