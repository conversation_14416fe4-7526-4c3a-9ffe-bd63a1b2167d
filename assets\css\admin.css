/* أنماط إضافة اختصار الروابط - لوحة الإدارة */

.ridcod-shorts-admin {
    direction: rtl;
    font-family: 'Segoe UI', <PERSON>homa, Arial, sans-serif;
}

.ridcod-shortcuts-admin * {
    box-sizing: border-box;
}

/* التبويبات المحدثة */
.ridcod-tabs {
    margin-top: 20px;
}

.nav-tab-wrapper {
    border-bottom: 1px solid #ccd0d4;
    margin-bottom: 0;
    background: #f9f9f9;
    padding: 0 10px;
    border-radius: 8px 8px 0 0;
}

.nav-tab {
    background: #f1f1f1;
    border: 1px solid #ccd0d4;
    color: #555;
    font-weight: 600;
    text-decoration: none;
    padding: 12px 20px;
    margin-left: 5px;
    margin-top: 5px;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    display: inline-block;
}

.nav-tab:hover {
    background: #fff;
    color: #333;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.nav-tab-active {
    background: #fff !important;
    border-bottom: 1px solid #fff;
    color: #0073aa !important;
    font-weight: 700;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
    z-index: 10;
    position: relative;
}

.nav-tab-active::before {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #0073aa, #005a87);
    border-radius: 2px 2px 0 0;
}

.tab-content {
    display: none;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-top: none;
    padding: 0;
    min-height: 400px;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.tab-content.active {
    display: block;
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* البطاقات المحدثة */
.ridcod-card {
    background: #fff;
    border: none;
    border-radius: 0 0 8px 8px;
    padding: 30px;
    margin: 0;
    box-shadow: none;
    transition: all 0.3s ease;
}

.ridcod-card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.ridcod-card h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 3px solid #0073aa;
    padding-bottom: 15px;
    margin-bottom: 25px;
    font-size: 24px;
    font-weight: 600;
    position: relative;
}

.ridcod-card h2::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #0073aa, #005a87);
    border-radius: 2px;
}

.ridcod-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* النماذج المحدثة */
.form-table {
    background: #fafafa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.form-table th {
    width: 220px;
    font-weight: 600;
    color: #333;
    padding: 20px 15px;
    vertical-align: top;
    background: #f8f9fa;
    border-radius: 6px 0 0 6px;
}

.form-table td {
    padding: 20px 15px;
    background: #fff;
    border-radius: 0 6px 6px 0;
}

.form-table tr {
    margin-bottom: 10px;
    display: block;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.form-table tr:last-child {
    margin-bottom: 0;
}

.form-table th,
.form-table td {
    display: inline-block;
    vertical-align: top;
}

.form-table input[type="url"],
.form-table input[type="text"],
.form-table input[type="number"],
.form-table select,
.form-table textarea {
    direction: ltr;
    text-align: left;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fff;
}

.form-table input:focus,
.form-table select:focus,
.form-table textarea:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
    outline: none;
}

.form-table input[type="color"] {
    width: 80px;
    height: 50px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-table input[type="color"]:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.form-table .description {
    margin-top: 8px;
    font-style: italic;
    color: #666;
    font-size: 13px;
    line-height: 1.5;
}

.short-code-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.short-url-prefix {
    background: #f1f1f1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-family: monospace;
    color: #666;
    white-space: nowrap;
}

.short-code-wrapper input {
    border-radius: 0 4px 4px 0 !important;
    border-left: none !important;
}

/* تحسين الأزرار */
.button-primary {
    background: linear-gradient(135deg, #0073aa, #005a87);
    border: none;
    border-radius: 8px;
    padding: 15px 25px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    color: white;
    text-shadow: none;
    box-shadow: 0 2px 8px rgba(0, 115, 170, 0.2);
}

.button-primary:hover,
.button-primary:focus {
    background: linear-gradient(135deg, #005a87, #004a73);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 115, 170, 0.3);
    color: white;
}

.button-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 115, 170, 0.2);
}

.button.button-large {
    padding: 15px 30px;
    font-size: 16px;
    height: auto;
    line-height: 1.4;
    border-radius: 8px;
}

.button .dashicons {
    vertical-align: middle;
    margin-left: 8px;
    font-size: 16px;
}

.button-secondary {
    border: 2px solid #0073aa;
    color: #0073aa;
    background: white;
    border-radius: 8px;
    padding: 12px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.button-secondary:hover {
    background: #0073aa;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 115, 170, 0.2);
}

/* نتيجة الرابط المختصر */
#short-url-result {
    margin-top: 20px;
    animation: slideInUp 0.5s ease-out;
}

.short-url-display {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-top: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.short-url-display:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.short-url-display input {
    flex: 1;
    font-family: monospace;
    background: #f9f9f9;
    border: 2px solid #0073aa;
    font-weight: bold;
}

#generated-short-url {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #2c3e50;
    background: white !important;
    border: 2px solid #28a745 !important;
    border-radius: 5px;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

#generated-short-url:focus {
    border-color: #20c997 !important;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

#copy-short-url {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white !important;
    border: none !important;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s ease;
    cursor: pointer;
}

#copy-short-url:hover {
    background: linear-gradient(135deg, #218838, #1ea886);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

#copy-short-url:active {
    transform: translateY(0);
}

/* الجداول */
.table-responsive {
    overflow-x: auto;
    margin-top: 20px;
}

.wp-list-table th,
.wp-list-table td {
    padding: 12px 8px;
    vertical-align: middle;
}

.wp-list-table th {
    background: #f1f1f1;
    font-weight: 600;
}

.wp-list-table code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    color: #666;
    display: block;
    margin-top: 5px;
}

/* الشارات */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-direct {
    background: #27ae60;
    color: white;
}

.badge-intermediate {
    background: #f39c12;
    color: white;
}

/* الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 48px;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0;
    font-size: 32px;
    font-weight: bold;
}

.stat-content p {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 14px;
}

/* تصميم متجاوب محدث */
@media (max-width: 768px) {
    .ridcod-card {
        padding: 20px 15px;
    }

    .nav-tab-wrapper {
        padding: 0 5px;
        overflow-x: auto;
        white-space: nowrap;
    }

    .nav-tab {
        margin-left: 3px;
        padding: 10px 15px;
        font-size: 14px;
        min-width: 120px;
        text-align: center;
    }

    .form-table {
        padding: 15px;
    }

    .form-table tr {
        display: block;
        margin-bottom: 15px;
    }

    .form-table th,
    .form-table td {
        display: block;
        width: 100%;
        padding: 15px;
        border-radius: 6px;
    }

    .form-table th {
        margin-bottom: 5px;
        background: #f0f6fc;
        border-radius: 6px 6px 0 0;
    }

    .form-table td {
        border-radius: 0 0 6px 6px;
        margin-bottom: 0;
    }

    .form-table input[type="text"],
    .form-table input[type="url"],
    .form-table input[type="number"],
    .form-table select,
    .form-table textarea {
        width: 100%;
        max-width: none;
    }

    .short-code-wrapper {
        flex-direction: column;
        align-items: stretch;
    }

    .short-url-prefix {
        border-radius: 6px 6px 0 0;
        text-align: center;
    }

    .short-code-wrapper input {
        border-radius: 0 0 6px 6px !important;
        border: 2px solid #e1e5e9 !important;
    }

    .short-url-display {
        flex-direction: column;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-box {
        flex-direction: column;
        text-align: center;
        gap: 10px;
        padding: 20px;
    }

    .wp-list-table {
        font-size: 14px;
    }

    .wp-list-table th,
    .wp-list-table td {
        padding: 8px 4px;
    }

    .button-primary,
    .button-secondary {
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
    }
}

@media (max-width: 480px) {
    .ridcod-card {
        padding: 15px 10px;
    }

    .ridcod-card h2 {
        font-size: 20px;
    }

    .nav-tab {
        padding: 8px 12px;
        font-size: 13px;
        min-width: 100px;
    }

    .form-table {
        padding: 10px;
    }

    .form-table th,
    .form-table td {
        padding: 12px;
    }
}

/* تحسينات إضافية */
.notice {
    border-right: 4px solid #0073aa;
    margin: 20px 0;
}

.notice-success {
    border-right-color: #27ae60;
}

.notice-error {
    border-right-color: #e74c3c;
}

.button-small {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
    line-height: 1.2;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #0073aa;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* رسائل التحميل */
.loading-message {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin: 20px 0;
    animation: fadeIn 0.3s ease-out;
}

.loading-message .dashicons {
    color: #0073aa;
    margin-bottom: 10px;
}

.loading-message p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* معاينة الرابط المختصر */
.short-url-preview {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #0073aa;
    border-radius: 12px;
    padding: 20px;
    margin-top: 15px;
    animation: slideInUp 0.4s ease-out;
    box-shadow: 0 4px 15px rgba(0, 115, 170, 0.1);
}

.preview-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    color: #0073aa;
    font-weight: 600;
}

.preview-header .dashicons {
    font-size: 18px;
    color: #0073aa;
}

.preview-url-container {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 12px;
}

.preview-url-input {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #2c3e50;
    background: white !important;
    border: 2px solid #0073aa !important;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
    direction: ltr;
    text-align: left;
}

.preview-url-input:focus {
    border-color: #005a87 !important;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
    outline: none;
}

.copy-preview-btn {
    background: linear-gradient(135deg, #0073aa, #005a87);
    color: white !important;
    border: none !important;
    padding: 12px 18px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 80px;
    justify-content: center;
    text-shadow: none;
}

.copy-preview-btn:hover {
    background: linear-gradient(135deg, #005a87, #004a73);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 115, 170, 0.3);
    color: white !important;
}

.copy-preview-btn:active {
    transform: translateY(0);
    color: white !important;
}

.copy-preview-btn:focus {
    color: white !important;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.3);
}

.copy-preview-btn .dashicons {
    font-size: 16px;
    margin: 0;
    color: white !important;
}

.preview-note {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    color: #856404;
    font-size: 13px;
    font-weight: 500;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 193, 7, 0.05));
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-left: 4px solid #ffc107;
    animation: fadeIn 0.3s ease-out;
}

.preview-note .dashicons {
    color: #ffc107;
    font-size: 16px;
}

/* تأثيرات النسخ الناجح */
.copy-preview-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    animation: pulse 0.6s ease-out;
    color: white !important;
}

.copy-preview-success .dashicons-admin-page::before {
    content: "\f147"; /* checkmark icon */
}

/* تحسين التصميم المتجاوب */
@media (max-width: 768px) {
    .preview-url-container {
        flex-direction: column;
        align-items: stretch;
    }

    .copy-preview-btn {
        width: 100%;
        justify-content: center;
    }

    .short-url-preview {
        padding: 15px;
    }

    .preview-header {
        font-size: 14px;
    }

    .preview-url-input {
        font-size: 13px;
        padding: 10px 12px;
    }
}

/* تحسين عرض النص العربي */
.ridcod-shorts-admin input,
.ridcod-shorts-admin textarea,
.ridcod-shorts-admin select {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
}

.ridcod-shorts-admin .regular-text,
.ridcod-shorts-admin .small-text {
    font-size: 14px;
}

/* ألوان متدرجة للبطاقات */
.stat-box:nth-child(1) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-box:nth-child(2) {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-box:nth-child(3) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* تأثيرات الحركة */
.ridcod-card,
.nav-tab,
.button {
    transition: all 0.3s ease;
}

.ridcod-card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.button:hover {
    transform: translateY(-1px);
}

/* تحسين تباعد العناصر */
.form-table tr:not(:last-child) td {
    border-bottom: 1px solid #f1f1f1;
}

.wp-list-table tbody tr:hover {
    background-color: #f9f9f9;
}

/* تحسين صندوق النتيجة */
#short-url-result {
    animation: slideInUp 0.5s ease-out;
}

#short-url-result .notice-success {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(70, 180, 80, 0.15);
    transition: all 0.3s ease;
}

#short-url-result .notice-success:hover {
    box-shadow: 0 6px 20px rgba(70, 180, 80, 0.2);
    transform: translateY(-2px);
}

.copy-success {
    animation: pulse 1s;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}
