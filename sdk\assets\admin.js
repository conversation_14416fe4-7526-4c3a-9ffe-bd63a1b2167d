/**
 * RIDCODTOKEN Plugin SDK - Admin JavaScript
 * إدارة واجهة تفعيل وإدارة التراخيص
 */

jQuery(document).ready(function($) {

    // تفعيل الترخيص (يعمل في الصفحة الموحدة)
    $(document).on('submit', '#ridcodtoken-activation-form', function(e) {
        e.preventDefault();

        var $form = $(this);
        var $button = $('#activate-license-btn');
        var $spinner = $button.find('.spinner');
        var $text = $button.find('.text');
        var $message = $('#license-message');
        var licenseKey = $('#license_key').val().trim();

        if (!licenseKey) {
            showMessage('يرجى إدخال مفتاح الترخيص', 'error');
            return;
        }

        // تعطيل النموذج وإظهار التحميل
        $button.prop('disabled', true);
        $spinner.show();
        $text.text(ridcodtoken_sdk.messages.activating);
        $message.empty();

        // إرسال طلب AJAX
        $.ajax({
            url: ridcodtoken_sdk.ajax_url,
            type: 'POST',
            data: {
                action: 'ridcodtoken_activate_license',
                license_key: licenseKey,
                nonce: ridcodtoken_sdk.nonce
            },
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    // إعادة تحميل الصفحة بعد 2 ثانية
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                } else {
                    showMessage(response.data.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                showMessage('حدث خطأ في الاتصال: ' + error, 'error');
            },
            complete: function() {
                // إعادة تفعيل النموذج
                $button.prop('disabled', false);
                $spinner.hide();
                $text.text('تفعيل الترخيص');
            }
        });
    });

    // فحص الترخيص (يعمل في الصفحة الموحدة)
    $(document).on('click', '#check-license-btn', function(e) {
        e.preventDefault();

        var $button = $(this);
        var $spinner = $button.find('.spinner');
        var $text = $button.find('.text');
        var $message = $('#license-message');

        // تعطيل الزر وإظهار التحميل
        $button.prop('disabled', true);
        $spinner.show();
        $text.text(ridcodtoken_sdk.messages.checking);
        $message.empty();

        // إرسال طلب AJAX لفحص الترخيص
        $.ajax({
            url: ridcodtoken_sdk.ajax_url,
            type: 'POST',
            data: {
                action: 'ridcodtoken_check_license_status',
                nonce: ridcodtoken_sdk.nonce
            },
            success: function(response) {
                if (response.success) {
                    showMessage('تم فحص الترخيص بنجاح. الترخيص صحيح ومفعل.', 'success');
                } else {
                    showMessage(response.data.message, 'error');
                    // إعادة تحميل الصفحة بعد 2 ثانية إذا فشل الترخيص
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                }
            },
            error: function(xhr, status, error) {
                showMessage('حدث خطأ في الاتصال: ' + error, 'error');
            },
            complete: function() {
                // إعادة تفعيل الزر
                $button.prop('disabled', false);
                $spinner.hide();
                $text.text('فحص الترخيص الآن');
            }
        });
    });

    // إلغاء تفعيل الترخيص (يعمل في الصفحة الموحدة)
    $(document).on('click', '#deactivate-license-btn', function(e) {
        e.preventDefault();

        if (!confirm(ridcodtoken_sdk.messages.confirm_deactivate)) {
            return;
        }

        var $button = $(this);
        var $message = $('#license-message');

        // تعطيل الزر
        $button.prop('disabled', true).text(ridcodtoken_sdk.messages.deactivating);
        $message.empty();

        // إرسال طلب AJAX
        $.ajax({
            url: ridcodtoken_sdk.ajax_url,
            type: 'POST',
            data: {
                action: 'ridcodtoken_deactivate_license',
                nonce: ridcodtoken_sdk.nonce
            },
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    // إعادة تحميل الصفحة بعد 2 ثانية
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                } else {
                    showMessage(response.data.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                showMessage('حدث خطأ في الاتصال: ' + error, 'error');
            },
            complete: function() {
                // إعادة تفعيل الزر
                $button.prop('disabled', false).text('إلغاء تفعيل الترخيص');
            }
        });
    });

    /**
     * عرض رسالة للمستخدم
     */
    function showMessage(message, type) {
        var $message = $('#license-message');
        var className = 'notice ';

        switch(type) {
            case 'success':
                className += 'notice-success';
                break;
            case 'error':
                className += 'notice-error';
                break;
            case 'warning':
                className += 'notice-warning';
                break;
            default:
                className += 'notice-info';
        }

        $message.html('<div class="' + className + '"><p>' + message + '</p></div>');

        // التمرير إلى الرسالة
        $('html, body').animate({
            scrollTop: $message.offset().top - 100
        }, 500);
    }

    // تحسين تجربة المستخدم - تنظيف الرسائل عند الكتابة
    $(document).on('input', '#license_key', function() {
        $('#license-message').empty();
    });

    // تفعيل الترخيص بالضغط على Enter
    $(document).on('keypress', '#license_key', function(e) {
        if (e.which === 13) {
            $('#ridcodtoken-activation-form').submit();
        }
    });

    // إضافة تأثيرات بصرية للأزرار
    $('.button').hover(
        function() {
            $(this).css('opacity', '0.8');
        },
        function() {
            $(this).css('opacity', '1');
        }
    );
});
