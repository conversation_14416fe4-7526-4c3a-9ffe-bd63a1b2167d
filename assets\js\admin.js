jQuery(document).ready(function($) {
    'use strict';
    
    // إدارة التبويبات المحدثة
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();

        // إزالة الفئة النشطة من جميع التبويبات
        $('.nav-tab').removeClass('nav-tab-active');
        $('.tab-content').removeClass('active');

        // إضافة الفئة النشطة للتبويب الحالي
        $(this).addClass('nav-tab-active');

        // عرض المحتوى المناسب
        const targetTab = $(this).data('tab');
        if (targetTab) {
            $('#' + targetTab).addClass('active');

            // تحديث البيانات عند التبديل للتبويبات
            if (targetTab === 'tab-list') {
                updateUrlsList();
            } else if (targetTab === 'tab-stats') {
                updateStatsData();
            }
        }

        // حفظ التبويبة النشطة
        localStorage.setItem('ridcod_active_tab', targetTab);
    });
    
    // إدارة خيارات نوع التحويل
    $('#redirect_type').on('change', function() {
        const redirectType = $(this).val();
        
        if (redirectType === 'intermediate') {
            $('#intermediate-options').slideDown();
            $('#intermediate_page_id').prop('required', true);
        } else {
            $('#intermediate-options').slideUp();
            $('#intermediate_page_id').prop('required', false);
        }
    });
    
    // توليد رمز عشوائي
    $('#generate-code').on('click', function(e) {
        e.preventDefault();

        const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';

        for (let i = 0; i < 6; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }

        $('#short_code').val(result);
        updatePreviewUrl(); // تحديث المعاينة
    });
    
    // إرسال نموذج إنشاء رابط مختصر
    $('#create-short-url-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const originalText = $submitBtn.html();
        
        // تعطيل النموذج وعرض حالة التحميل
        $submitBtn.addClass('loading').prop('disabled', true);
        $submitBtn.html('<span class="dashicons dashicons-update"></span> جاري الإنشاء...');
        
        // جمع بيانات النموذج
        const formData = {
            action: 'create_short_url',
            nonce: $('#nonce').val(),
            original_url: $('#original_url').val(),
            redirect_type: $('#redirect_type').val(),
            short_code: $('#short_code').val(),
            intermediate_page_id: $('#intermediate_page_id').val()
        };
        
        // إرسال طلب AJAX
        $.ajax({
            url: ridcod_shorts_ajax.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    // عرض النتيجة
                    $('#generated-short-url').val(response.data.short_url);
                    $('#short-url-result').slideDown(function() {
                        // إطلاق حدث مخصص عند إظهار النتيجة
                        $(document).trigger('ridcod:result-shown');
                    });

                    // إعادة تعيين النموذج
                    $form[0].reset();
                    $('#redirect_type').trigger('change');

                    // إخفاء معاينة الرابط
                    $('#short-url-preview').slideUp(300);

                    // تحديث قائمة الروابط والإحصائيات
                    updateUrlsList();
                    updateStatsData();

                    // التمرير إلى النتيجة
                    $('html, body').animate({
                        scrollTop: $('#short-url-result').offset().top - 100
                    }, 500);
                } else {
                    showNotice('خطأ: ' + response.data, 'error');
                }
            },
            error: function(xhr, status, error) {
                showNotice('حدث خطأ أثناء إنشاء الرابط المختصر', 'error');
                console.error('AJAX Error:', error);
            },
            complete: function() {
                // إعادة تفعيل النموذج
                $submitBtn.removeClass('loading').prop('disabled', false);
                $submitBtn.html(originalText);
            }
        });
    });
    
    // نسخ الرابط المختصر (استخدام event delegation للأزرار المضافة ديناميكياً)
    $(document).on('click', '#copy-short-url, .copy-url-btn', function(e) {
        e.preventDefault();
        
        const $btn = $(this);
        const originalText = $btn.html();
        
        let urlToCopy;
        
        if ($(this).is('#copy-short-url')) {
            urlToCopy = $('#generated-short-url').val();
        } else {
            urlToCopy = $(this).data('url');
        }
        
        // التحقق من وجود الرابط
        if (!urlToCopy) {
            console.error('لا يوجد رابط للنسخ');
            return;
        }
        
        console.log('جاري نسخ الرابط:', urlToCopy);
        
        // تأثير بصري أثناء النسخ
        $btn.html('📋 جاري النسخ...');
        
        // نسخ إلى الحافظة
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(urlToCopy).then(function() {
                $btn.html('✅ تم النسخ!').addClass('copy-success');
                
                setTimeout(function() {
                    $btn.html(originalText).removeClass('copy-success');
                }, 2000);
            }).catch(function(err) {
                console.error('خطأ في النسخ:', err);
                fallbackCopyTextToClipboard(urlToCopy, $btn, originalText);
            });
        } else {
            console.log('استخدام الطريقة الاحتياطية للنسخ');
            fallbackCopyTextToClipboard(urlToCopy, $btn, originalText);
        }
    });
    
    // حذف رابط مختصر
    $('.delete-url-btn').on('click', function(e) {
        e.preventDefault();
        
        const urlId = $(this).data('id');
        const $row = $(this).closest('tr');
        
        if (!confirm('هل أنت متأكد من حذف هذا الرابط المختصر؟')) {
            return;
        }
        
        const $btn = $(this);
        const originalText = $btn.text();
        
        $btn.text('جاري الحذف...').prop('disabled', true);
        
        $.ajax({
            url: ridcod_shorts_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'delete_short_url',
                nonce: ridcod_shorts_ajax.nonce,
                id: urlId
            },
            success: function(response) {
                if (response.success) {
                    $row.fadeOut(function() {
                        $(this).remove();
                        // تحديث الإحصائيات بعد الحذف
                        updateStatsData();
                    });
                    showNotice('تم حذف الرابط بنجاح', 'success');
                } else {
                    showNotice('خطأ: ' + response.data, 'error');
                    $btn.text(originalText).prop('disabled', false);
                }
            },
            error: function() {
                showNotice('حدث خطأ أثناء حذف الرابط', 'error');
                $btn.text(originalText).prop('disabled', false);
            }
        });
    });
    
    // وظيفة نسخ احتياطية للمتصفحات القديمة
    function fallbackCopyTextToClipboard(text, $btn, originalText) {
        console.log('استخدام الطريقة الاحتياطية لنسخ:', text);
        
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        textArea.style.opacity = "0";
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        textArea.setSelectionRange(0, 99999); // للهواتف المحمولة
        
        try {
            const successful = document.execCommand('copy');
            console.log('نتيجة النسخ الاحتياطي:', successful);
            if (successful) {
                if ($btn && originalText) {
                    $btn.html('✅ تم النسخ!').addClass('copy-success');
                    setTimeout(function() {
                        $btn.html(originalText).removeClass('copy-success');
                    }, 2000);
                }
            } else {
                if ($btn && originalText) {
                    $btn.html(originalText);
                }
            }
        } catch (err) {
            console.error('خطأ في النسخ الاحتياطي:', err);
            if ($btn && originalText) {
                $btn.html(originalText);
            }
        }
        
        document.body.removeChild(textArea);
    }
    
    // عرض الإشعارات
    function showNotice(message, type) {
        type = type || 'info';
        
        const noticeClass = 'notice notice-' + type;
        const notice = $('<div class="' + noticeClass + ' is-dismissible"><p>' + message + '</p></div>');
        
        // إزالة الإشعارات السابقة
        $('.notice').remove();
        
        // إضافة الإشعار الجديد
        $('.wrap h1').after(notice);
        
        // إخفاء الإشعار بعد 5 ثوانٍ
        setTimeout(function() {
            notice.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
        
        // التمرير إلى أعلى الصفحة
        $('html, body').animate({
            scrollTop: 0
        }, 500);
    }
    
    // التحقق من صحة الرابط
    $('#original_url').on('blur', function() {
        const url = $(this).val();
        const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
        
        if (url && !urlPattern.test(url)) {
            $(this).addClass('error');
            showNotice('يرجى إدخال رابط صحيح', 'error');
        } else {
            $(this).removeClass('error');
        }
    });
    
    // التحقق من توفر الرمز المختصر
    let checkCodeTimeout;
    $('#short_code').on('input', function() {
        const code = $(this).val();
        const $field = $(this);
        
        clearTimeout(checkCodeTimeout);
        
        if (code.length >= 3) {
            checkCodeTimeout = setTimeout(function() {
                // يمكن إضافة فحص AJAX هنا للتحقق من توفر الرمز
                // في الوقت الحالي نكتفي بالفحص عند الإرسال
            }, 500);
        }
    });
    
    // تحسين تجربة المستخدم
    $(document).on('ajaxStart', function() {
        $('body').addClass('loading');
    }).on('ajaxStop', function() {
        $('body').removeClass('loading');
    });
    
    // إضافة تأثيرات للأزرار
    $('.button').on('mouseenter', function() {
        $(this).addClass('hover');
    }).on('mouseleave', function() {
        $(this).removeClass('hover');
    });
    
    // تحسين إمكانية الوصول
    $('.nav-tab').on('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            $(this).click();
        }
    });
    
    // استعادة التبويبة النشطة من localStorage
    const savedTab = localStorage.getItem('ridcod_active_tab');
    if (savedTab) {
        const $savedTabElement = $('.nav-tab[data-tab="' + savedTab + '"]');
        const $savedContentElement = $('#' + savedTab);

        if ($savedTabElement.length && $savedContentElement.length) {
            // إزالة الفئة النشطة من جميع التبويبات
            $('.nav-tab').removeClass('nav-tab-active');
            $('.tab-content').removeClass('active');

            // تفعيل التبويبة المحفوظة
            $savedTabElement.addClass('nav-tab-active');
            $savedContentElement.addClass('active');
        }
    }
    
    // تهيئة الصفحة
    $('#redirect_type').trigger('change');
    
    // إضافة تأثيرات لطيفة عند تحميل الصفحة
    $('.ridcod-card').each(function(index) {
        $(this).css('opacity', '0').delay(index * 100).animate({
            opacity: 1
        }, 500);
    });

    // دالة تحديث قائمة الروابط
    window.updateUrlsList = function() {
        // إظهار رسالة تحميل
        $('#tab-list .ridcod-card').html(
            '<h2>قائمة الروابط المختصرة</h2>' +
            '<div class="loading-message" style="text-align: center; padding: 40px;">' +
            '<span class="dashicons dashicons-update" style="animation: spin 1s linear infinite; font-size: 24px; color: #0073aa;"></span>' +
            '<p style="margin-top: 10px; color: #666;">جاري تحديث قائمة الروابط...</p>' +
            '</div>'
        );

        $.ajax({
            url: ridcod_shorts_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_short_urls_list',
                nonce: ridcod_shorts_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // تحديث محتوى تبويبة قائمة الروابط
                    $('#tab-list .ridcod-card').html(
                        '<h2>قائمة الروابط المختصرة</h2>' +
                        response.data.html
                    );

                    // إعادة ربط أحداث الأزرار الجديدة
                    bindUrlListEvents();
                }
            },
            error: function() {
                console.error('خطأ في تحديث قائمة الروابط');
                $('#tab-list .ridcod-card').html(
                    '<h2>قائمة الروابط المختصرة</h2>' +
                    '<div class="notice notice-error"><p>حدث خطأ أثناء تحديث قائمة الروابط</p></div>'
                );
            }
        });
    }

    // دالة تحديث الإحصائيات
    window.updateStatsData = function() {
        // إظهار رسالة تحميل
        $('#tab-stats .ridcod-card').html(
            '<h2>الإحصائيات العامة</h2>' +
            '<div class="loading-message" style="text-align: center; padding: 40px;">' +
            '<span class="dashicons dashicons-update" style="animation: spin 1s linear infinite; font-size: 24px; color: #0073aa;"></span>' +
            '<p style="margin-top: 10px; color: #666;">جاري تحديث الإحصائيات...</p>' +
            '</div>'
        );

        $.ajax({
            url: ridcod_shorts_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_stats_data',
                nonce: ridcod_shorts_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // تحديث محتوى تبويبة الإحصائيات
                    $('#tab-stats .ridcod-card').html(
                        '<h2>الإحصائيات العامة</h2>' +
                        response.data.html
                    );
                }
            },
            error: function() {
                console.error('خطأ في تحديث الإحصائيات');
                $('#tab-stats .ridcod-card').html(
                    '<h2>الإحصائيات العامة</h2>' +
                    '<div class="notice notice-error"><p>حدث خطأ أثناء تحديث الإحصائيات</p></div>'
                );
            }
        });
    }

    // دالة ربط أحداث قائمة الروابط
    function bindUrlListEvents() {
        // ربط أحداث نسخ الروابط
        $('.copy-url-btn').off('click').on('click', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const originalText = $btn.html();
            const urlToCopy = $(this).data('url');

            if (!urlToCopy) {
                console.error('لا يوجد رابط للنسخ');
                return;
            }

            $btn.html('📋 جاري النسخ...');

            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(urlToCopy).then(function() {
                    $btn.html('✅ تم النسخ!').addClass('copy-success');

                    setTimeout(function() {
                        $btn.html(originalText).removeClass('copy-success');
                    }, 2000);
                }).catch(function(err) {
                    console.error('خطأ في النسخ:', err);
                    fallbackCopyTextToClipboard(urlToCopy, $btn, originalText);
                });
            } else {
                fallbackCopyTextToClipboard(urlToCopy, $btn, originalText);
            }
        });

        // ربط أحداث حذف الروابط
        $('.delete-url-btn').off('click').on('click', function(e) {
            e.preventDefault();

            const urlId = $(this).data('id');
            const $row = $(this).closest('tr');

            if (!confirm('هل أنت متأكد من حذف هذا الرابط المختصر؟')) {
                return;
            }

            const $btn = $(this);
            const originalText = $btn.text();

            $btn.text('جاري الحذف...').prop('disabled', true);

            $.ajax({
                url: ridcod_shorts_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'delete_short_url',
                    nonce: ridcod_shorts_ajax.nonce,
                    id: urlId
                },
                success: function(response) {
                    if (response.success) {
                        $row.fadeOut(function() {
                            $(this).remove();
                            // تحديث الإحصائيات بعد الحذف
                            updateStatsData();
                        });
                        showNotice('تم حذف الرابط بنجاح', 'success');
                    } else {
                        showNotice('خطأ: ' + response.data, 'error');
                        $btn.text(originalText).prop('disabled', false);
                    }
                },
                error: function() {
                    showNotice('حدث خطأ أثناء حذف الرابط', 'error');
                    $btn.text(originalText).prop('disabled', false);
                }
            });
        });
    }

    // ربط الأحداث عند تحميل الصفحة
    bindUrlListEvents();

    // إدارة معاينة الرابط المختصر
    $('#short_code').on('input', function() {
        updatePreviewUrl();
    });

    // نسخ رابط المعاينة
    $('#copy-preview-url').on('click', function(e) {
        e.preventDefault();
        copyPreviewUrl();
    });

    // دالة تحديث معاينة الرابط
    function updatePreviewUrl() {
        const shortCode = $('#short_code').val().trim();
        const baseUrl = ridcod_shorts_ajax.home_url || window.location.origin;

        if (shortCode) {
            const previewUrl = baseUrl + '/' + shortCode;
            $('#preview-url').val(previewUrl);
            $('#short-url-preview').slideDown(300);
        } else {
            $('#short-url-preview').slideUp(300);
        }
    }

    // دالة نسخ رابط المعاينة
    function copyPreviewUrl() {
        const $btn = $('#copy-preview-url');
        const $input = $('#preview-url');
        const originalHtml = $btn.html();
        const urlToCopy = $input.val();

        if (!urlToCopy) {
            return;
        }

        // تأثير بصري أثناء النسخ
        $btn.html('<span class="dashicons dashicons-update"></span> جاري النسخ...');
        $btn.prop('disabled', true);

        // نسخ إلى الحافظة
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(urlToCopy).then(function() {
                showCopySuccess($btn, originalHtml);
            }).catch(function(err) {
                console.error('خطأ في النسخ:', err);
                fallbackCopyTextToClipboard(urlToCopy, $btn, originalHtml);
            });
        } else {
            fallbackCopyTextToClipboard(urlToCopy, $btn, originalHtml);
        }
    }

    // دالة إظهار نجاح النسخ
    function showCopySuccess($btn, originalHtml) {
        $btn.html('<span class="dashicons dashicons-yes"></span> تم النسخ!');
        $btn.addClass('copy-preview-success');
        $btn.css('color', 'white'); // ضمان بقاء النص أبيض

        setTimeout(function() {
            $btn.html(originalHtml);
            $btn.removeClass('copy-preview-success');
            $btn.css('color', ''); // إعادة تعيين اللون
            $btn.prop('disabled', false);
        }, 2000);
    }

    // تهيئة المعاينة عند تحميل الصفحة
    updatePreviewUrl();

});
