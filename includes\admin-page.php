<?php
// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// الحصول على جميع الصفحات
$pages = get_pages();

// الحصول على جميع الروابط المختصرة
$short_urls = RidcodShorts::get_all_short_urls();
?>

<div class="wrap ridcod-shorts-admin">
    <h1><?php _e('اختصار الروابط', 'ridcod-shorts'); ?></h1>
    
    <div class="ridcod-tabs">
        <nav class="nav-tab-wrapper">
            <a href="#tab-create" class="nav-tab nav-tab-active" data-tab="tab-create"><?php _e('إنشاء رابط مختصر', 'ridcod-shorts'); ?></a>
            <a href="#tab-list" class="nav-tab" data-tab="tab-list"><?php _e('قائمة الروابط', 'ridcod-shorts'); ?></a>
            <a href="#tab-stats" class="nav-tab" data-tab="tab-stats"><?php _e('الإحصائيات', 'ridcod-shorts'); ?></a>
        </nav>

        <!-- تبويب إنشاء رابط مختصر -->
        <div id="tab-create" class="tab-content active">
            <div class="ridcod-card">
                <h2><?php _e('إنشاء رابط مختصر جديد', 'ridcod-shorts'); ?></h2>
                
                <form id="create-short-url-form">
                    <?php wp_nonce_field('ridcod_shorts_nonce', 'nonce'); ?>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="original_url"><?php _e('الرابط الأصلي', 'ridcod-shorts'); ?></label>
                            </th>
                            <td>
                                <input type="url" id="original_url" name="original_url" class="regular-text" required placeholder="https://example.com">
                                <p class="description"><?php _e('أدخل الرابط الطويل الذي تريد اختصاره', 'ridcod-shorts'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="redirect_type"><?php _e('نوع التحويل', 'ridcod-shorts'); ?></label>
                            </th>
                            <td>
                                <select id="redirect_type" name="redirect_type" required>
                                    <option value="direct"><?php _e('تحويل مباشر', 'ridcod-shorts'); ?></option>
                                    <option value="intermediate"><?php _e('تحويل عبر صفحة وسيطة', 'ridcod-shorts'); ?></option>
                                </select>
                                <p class="description"><?php _e('اختر نوع التحويل المطلوب', 'ridcod-shorts'); ?></p>
                            </td>
                        </tr>
                        
                        <tr id="intermediate-options" style="display: none;">
                            <th scope="row">
                                <label for="intermediate_page_id"><?php _e('الصفحة الوسيطة', 'ridcod-shorts'); ?></label>
                            </th>
                            <td>
                                <select id="intermediate_page_id" name="intermediate_page_id">
                                    <option value=""><?php _e('اختر صفحة', 'ridcod-shorts'); ?></option>
                                    <option value="random_post"><?php _e('🎲 مقال عشوائي', 'ridcod-shorts'); ?></option>
                                    <optgroup label="<?php _e('الصفحات', 'ridcod-shorts'); ?>">
                                        <?php foreach ($pages as $page): ?>
                                            <option value="<?php echo $page->ID; ?>"><?php echo esc_html($page->post_title); ?></option>
                                        <?php endforeach; ?>
                                    </optgroup>
                                    <optgroup label="<?php _e('المقالات الحديثة', 'ridcod-shorts'); ?>">
                                        <?php 
                                        $recent_posts = get_posts(array(
                                            'numberposts' => 10,
                                            'post_status' => 'publish',
                                            'orderby' => 'date',
                                            'order' => 'DESC'
                                        ));
                                        foreach ($recent_posts as $post): ?>
                                            <option value="<?php echo $post->ID; ?>"><?php echo esc_html($post->post_title); ?></option>
                                        <?php endforeach; ?>
                                    </optgroup>
                                </select>
                                <p class="description"><?php _e('اختر الصفحة/المقال الذي سيتم عرضه قبل التحويل. "مقال عشوائي" سيختار مقال مختلف في كل مرة.', 'ridcod-shorts'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="short_code"><?php _e('الرمز المختصر', 'ridcod-shorts'); ?></label>
                            </th>
                            <td>
                                <div class="short-code-wrapper">
                                    <span class="short-url-prefix"><?php echo home_url('/'); ?></span>
                                    <input type="text" id="short_code" name="short_code" class="regular-text" placeholder="<?php _e('سيتم توليده تلقائياً', 'ridcod-shorts'); ?>">
                                    <button type="button" id="generate-code" class="button button-secondary"><?php _e('توليد عشوائي', 'ridcod-shorts'); ?></button>
                                </div>
                                <p class="description"><?php _e('اتركه فارغاً لتوليد رمز عشوائي، أو أدخل رمز مخصص', 'ridcod-shorts'); ?></p>

                                <!-- معاينة الرابط المختصر -->
                                <div id="short-url-preview" class="short-url-preview" style="display: none;">
                                    <div class="preview-header">
                                        <span class="dashicons dashicons-visibility"></span>
                                        <strong><?php _e('معاينة الرابط المختصر:', 'ridcod-shorts'); ?></strong>
                                    </div>
                                    <div class="preview-url-container">
                                        <input type="text" id="preview-url" readonly class="preview-url-input" value="">
                                        <button type="button" id="copy-preview-url" class="button button-secondary copy-preview-btn">
                                            <span class="dashicons dashicons-admin-page"></span>
                                            <?php _e('نسخ', 'ridcod-shorts'); ?>
                                        </button>
                                    </div>
                                    <p class="preview-note">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('هذا مجرد معاينة - يجب إنشاء الرابط أولاً ليعمل', 'ridcod-shorts'); ?>
                                    </p>
                                </div>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <button type="submit" class="button button-primary button-large">
                            <span class="dashicons dashicons-admin-links"></span>
                            <?php _e('إنشاء الرابط المختصر', 'ridcod-shorts'); ?>
                        </button>
                    </p>
                </form>
                
                <div id="short-url-result" style="display: none;">
                    <div class="notice notice-success" style="margin-top: 20px; padding: 15px; border-left: 4px solid #46b450;">
                        <div class="short-url-display" style="background: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;">
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <input type="text" id="generated-short-url" readonly class="regular-text" style="flex: 1; background: white; border: 1px solid #ddd;">
                                <button type="button" id="copy-short-url" class="button button-secondary" style="min-width: 80px;">
                                    نسخ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- تبويب قائمة الروابط -->
        <div id="tab-list" class="tab-content">
            <div class="ridcod-card">
                <h2><?php _e('قائمة الروابط المختصرة', 'ridcod-shorts'); ?></h2>
                
                <?php if (empty($short_urls)): ?>
                    <div class="notice notice-info">
                        <p><?php _e('لا توجد روابط مختصرة بعد. ابدأ بإنشاء أول رابط!', 'ridcod-shorts'); ?></p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th scope="col"><?php _e('الرمز المختصر', 'ridcod-shorts'); ?></th>
                                    <th scope="col"><?php _e('الرابط الأصلي', 'ridcod-shorts'); ?></th>
                                    <th scope="col"><?php _e('نوع التحويل', 'ridcod-shorts'); ?></th>
                                    <th scope="col"><?php _e('عدد النقرات', 'ridcod-shorts'); ?></th>
                                    <th scope="col"><?php _e('تاريخ الإنشاء', 'ridcod-shorts'); ?></th>
                                    <th scope="col"><?php _e('الإجراءات', 'ridcod-shorts'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($short_urls as $url): ?>
                                    <tr>
                                        <td>
                                            <strong>
                                                <a href="<?php echo home_url('/' . $url->short_code); ?>" target="_blank">
                                                    <?php echo esc_html($url->short_code); ?>
                                                    <span class="dashicons dashicons-external"></span>
                                                </a>
                                            </strong>
                                            <br>
                                            <code><?php echo home_url('/' . $url->short_code); ?></code>
                                        </td>
                                        <td>
                                            <a href="<?php echo esc_url($url->original_url); ?>" target="_blank" title="<?php echo esc_attr($url->original_url); ?>">
                                                <?php echo esc_html(wp_trim_words($url->original_url, 8, '...')); ?>
                                            </a>
                                        </td>
                                        <td>
                                            <?php if ($url->redirect_type === 'direct'): ?>
                                                <span class="badge badge-direct"><?php _e('مباشر', 'ridcod-shorts'); ?></span>
                                            <?php else: ?>
                                                <span class="badge badge-intermediate"><?php _e('صفحة وسيطة', 'ridcod-shorts'); ?></span>
                                                <?php if ($url->intermediate_page_id === 'random_post'): ?>
                                                    <br><small>🎲 <?php _e('مقال عشوائي', 'ridcod-shorts'); ?></small>
                                                <?php elseif ($url->intermediate_page_id): ?>
                                                    <br><small><?php echo get_the_title($url->intermediate_page_id); ?></small>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo number_format($url->clicks); ?></strong>
                                        </td>
                                        <td>
                                            <?php echo wp_date('Y/m/d H:i', strtotime($url->created_at)); ?>
                                        </td>
                                        <td>
                                            <button type="button" class="button button-small copy-url-btn" data-url="<?php echo home_url('/' . $url->short_code); ?>">
                                                <?php _e('نسخ', 'ridcod-shorts'); ?>
                                            </button>
                                            <button type="button" class="button button-small button-link-delete delete-url-btn" data-id="<?php echo $url->id; ?>">
                                                <?php _e('حذف', 'ridcod-shorts'); ?>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- تبويب الإحصائيات -->
        <div id="tab-stats" class="tab-content">
            <div class="ridcod-card">
                <h2><?php _e('الإحصائيات العامة', 'ridcod-shorts'); ?></h2>
                
                <?php
                $total_urls = count($short_urls);
                $total_clicks = array_sum(array_column($short_urls, 'clicks'));
                $avg_clicks = $total_urls > 0 ? round($total_clicks / $total_urls, 2) : 0;
                ?>
                
                <div class="stats-grid">
                    <div class="stat-box">
                        <div class="stat-icon">
                            <span class="dashicons dashicons-admin-links"></span>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($total_urls); ?></h3>
                            <p><?php _e('إجمالي الروابط', 'ridcod-shorts'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-box">
                        <div class="stat-icon">
                            <span class="dashicons dashicons-chart-line"></span>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($total_clicks); ?></h3>
                            <p><?php _e('إجمالي النقرات', 'ridcod-shorts'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-box">
                        <div class="stat-icon">
                            <span class="dashicons dashicons-chart-bar"></span>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $avg_clicks; ?></h3>
                            <p><?php _e('متوسط النقرات', 'ridcod-shorts'); ?></p>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($short_urls)): ?>
                    <h3><?php _e('أكثر الروابط استخداماً', 'ridcod-shorts'); ?></h3>
                    <div class="top-urls">
                        <?php
                        usort($short_urls, function($a, $b) {
                            return $b->clicks - $a->clicks;
                        });
                        $top_urls = array_slice($short_urls, 0, 5);
                        ?>
                        
                        <table class="wp-list-table widefat">
                            <thead>
                                <tr>
                                    <th><?php _e('الرابط المختصر', 'ridcod-shorts'); ?></th>
                                    <th><?php _e('عدد النقرات', 'ridcod-shorts'); ?></th>
                                    <th><?php _e('النسبة المئوية', 'ridcod-shorts'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($top_urls as $url): ?>
                                    <tr>
                                        <td>
                                            <a href="<?php echo home_url('/' . $url->short_code); ?>" target="_blank">
                                                <?php echo esc_html($url->short_code); ?>
                                            </a>
                                        </td>
                                        <td><strong><?php echo number_format($url->clicks); ?></strong></td>
                                        <td>
                                            <?php 
                                            $percentage = $total_clicks > 0 ? round(($url->clicks / $total_clicks) * 100, 1) : 0;
                                            echo $percentage . '%';
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
