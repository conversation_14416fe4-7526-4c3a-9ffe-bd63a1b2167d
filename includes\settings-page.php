<?php
// منع الوصول المباشر للملف
if (!defined('ABSPATH')) {
    exit;
}

// التحقق من الصلاحيات
if (!current_user_can('manage_options')) {
    wp_die(__('Access denied', 'ridcod-shorts'));
}

// معالجة حفظ الإعدادات العامة
if (isset($_POST['save_general_settings']) && wp_verify_nonce($_POST['ridcod_shorts_general_nonce'], 'ridcod_shorts_general_settings')) {
    $settings = get_option('ridcod_shorts_settings', array());

    // إعدادات العامة
    $settings['default_countdown'] = absint($_POST['default_countdown'] ?? 5);

    update_option('ridcod_shorts_settings', $settings);

    echo '<div class="notice notice-success is-dismissible"><p>' . __('تم حفظ الإعدادات العامة بنجاح!', 'ridcod-shorts') . '</p></div>';
}

// معالجة حفظ إعدادات النصوص
if (isset($_POST['save_text_settings']) && wp_verify_nonce($_POST['ridcod_shorts_text_nonce'], 'ridcod_shorts_text_settings')) {
    $settings = get_option('ridcod_shorts_settings', array());

    // إعدادات النصوص
    $settings['countdown_text'] = sanitize_text_field($_POST['countdown_text'] ?? '');
    $settings['continue_text'] = sanitize_text_field($_POST['continue_text'] ?? '');
    $settings['redirect_text'] = sanitize_textarea_field($_POST['redirect_text'] ?? '');
    $settings['button_text'] = sanitize_text_field($_POST['button_text'] ?? '');

    update_option('ridcod_shorts_settings', $settings);

    echo '<div class="notice notice-success is-dismissible"><p>' . __('تم حفظ إعدادات النصوص بنجاح!', 'ridcod-shorts') . '</p></div>';
}

// معالجة حفظ إعدادات التصميم
if (isset($_POST['save_design_settings']) && wp_verify_nonce($_POST['ridcod_shorts_design_nonce'], 'ridcod_shorts_design_settings')) {
    $settings = get_option('ridcod_shorts_settings', array());

    // إعدادات التصميم
    $settings['countdown_color'] = sanitize_hex_color($_POST['countdown_color'] ?? '#007cba');
    $settings['button_color'] = sanitize_hex_color($_POST['button_color'] ?? '#007cba');
    $settings['text_color'] = sanitize_hex_color($_POST['text_color'] ?? '#333333');

    update_option('ridcod_shorts_settings', $settings);

    echo '<div class="notice notice-success is-dismissible"><p>' . __('تم حفظ إعدادات التصميم بنجاح!', 'ridcod-shorts') . '</p></div>';
}

// معالجة إعادة تعيين إعدادات التصميم
if (isset($_POST['reset_design_settings']) && wp_verify_nonce($_POST['ridcod_shorts_design_nonce'], 'ridcod_shorts_design_settings')) {
    $settings = get_option('ridcod_shorts_settings', array());

    // إعادة تعيين إعدادات التصميم للقيم الافتراضية
    $settings['countdown_color'] = '#007cba';
    $settings['button_color'] = '#007cba';
    $settings['text_color'] = '#333333';

    update_option('ridcod_shorts_settings', $settings);

    echo '<div class="notice notice-success is-dismissible"><p>' . __('تم إعادة تعيين إعدادات التصميم للقيم الافتراضية بنجاح!', 'ridcod-shorts') . '</p></div>';

    // إعادة تحميل الصفحة لإظهار القيم الجديدة
    echo '<script>setTimeout(function(){ window.location.reload(); }, 1500);</script>';
}

// معالجة إعادة تعيين إعدادات النصوص
if (isset($_POST['reset_text_settings']) && wp_verify_nonce($_POST['ridcod_shorts_text_nonce'], 'ridcod_shorts_text_settings')) {
    $settings = get_option('ridcod_shorts_settings', array());

    // إعادة تعيين إعدادات النصوص للقيم الافتراضية
    $settings['countdown_text'] = __('سيتم عرض المحتوى خلال:', 'ridcod-shorts');
    $settings['continue_text'] = __('متابعة', 'ridcod-shorts');
    $settings['redirect_text'] = __('انقر على الزر أدناه للانتقال إلى الرابط المطلوب', 'ridcod-shorts');
    $settings['button_text'] = __('الانتقال إلى الرابط', 'ridcod-shorts');

    update_option('ridcod_shorts_settings', $settings);

    echo '<div class="notice notice-success is-dismissible"><p>' . __('تم إعادة تعيين إعدادات النصوص للقيم الافتراضية بنجاح!', 'ridcod-shorts') . '</p></div>';

    // إعادة تحميل الصفحة لإظهار القيم الجديدة
    echo '<script>setTimeout(function(){ window.location.reload(); }, 1500);</script>';
}

// الحصول على الإعدادات الحالية
$settings = get_option('ridcod_shorts_settings', array(
    'default_countdown' => 5,
    'countdown_text' => __('سيتم عرض المحتوى خلال:', 'ridcod-shorts'),
    'continue_text' => __('متابعة', 'ridcod-shorts'),
    'redirect_text' => __('انقر على الزر أدناه للانتقال إلى الرابط المطلوب', 'ridcod-shorts'),
    'button_text' => __('الانتقال إلى الرابط', 'ridcod-shorts'),
    'countdown_color' => '#007cba',
    'button_color' => '#007cba',
    'text_color' => '#333333'
));
?>

<div class="wrap ridcod-shorts-admin">
    <h1><?php _e('إعدادات اختصار الروابط', 'ridcod-shorts'); ?></h1>

    <!-- التبويبات -->
    <nav class="nav-tab-wrapper">
        <a href="#general" class="nav-tab nav-tab-active" data-tab="general">
            <?php _e('الإعدادات العامة', 'ridcod-shorts'); ?>
        </a>
        <a href="#texts" class="nav-tab" data-tab="texts">
            <?php _e('النصوص', 'ridcod-shorts'); ?>
        </a>
        <a href="#design" class="nav-tab" data-tab="design">
            <?php _e('التصميم', 'ridcod-shorts'); ?>
        </a>
    </nav>

    <!-- تبويبة الإعدادات العامة -->
    <div id="general" class="tab-content active">
        <div class="ridcod-card">
            <h2><?php _e('الإعدادات العامة', 'ridcod-shorts'); ?></h2>

            <form method="post" action="">
                <?php wp_nonce_field('ridcod_shorts_general_settings', 'ridcod_shorts_general_nonce'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="default_countdown"><?php _e('العداد التنازلي الافتراضي (ثواني)', 'ridcod-shorts'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="default_countdown" name="default_countdown"
                                   value="<?php echo esc_attr($settings['default_countdown']); ?>"
                                   min="1" max="60" class="small-text" />
                            <p class="description">
                                <?php _e('المدة الافتراضية للعداد التنازلي قبل ظهور زر المتابعة (من 1 إلى 60 ثانية)', 'ridcod-shorts'); ?>
                            </p>
                        </td>
                    </tr>
                </table>

                <?php submit_button(__('حفظ الإعدادات العامة', 'ridcod-shorts'), 'primary', 'save_general_settings'); ?>
            </form>
        </div>
    </div>

    <!-- تبويبة النصوص -->
                <div id="texts" class="tab-content">
        <div class="ridcod-card">
            <h2><?php _e('تخصيص النصوص', 'ridcod-shorts'); ?></h2>
            <p><?php _e('يمكنك تخصيص النصوص التي تظهر في صفحة الانتظار الوسيطة', 'ridcod-shorts'); ?></p>

            <form method="post" action="">
                <?php wp_nonce_field('ridcod_shorts_text_settings', 'ridcod_shorts_text_nonce'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="countdown_text"><?php _e('نص العداد التنازلي', 'ridcod-shorts'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="countdown_text" name="countdown_text"
                                   value="<?php echo esc_attr($settings['countdown_text']); ?>"
                                   class="regular-text" />
                            <p class="description">
                                <?php _e('النص الذي يظهر مع العداد التنازلي', 'ridcod-shorts'); ?>
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="continue_text"><?php _e('نص زر المتابعة', 'ridcod-shorts'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="continue_text" name="continue_text"
                                   value="<?php echo esc_attr($settings['continue_text']); ?>"
                                   class="regular-text" />
                            <p class="description">
                                <?php _e('النص الذي يظهر على زر المتابعة', 'ridcod-shorts'); ?>
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="redirect_text"><?php _e('نص التوجيه', 'ridcod-shorts'); ?></label>
                        </th>
                        <td>
                            <textarea id="redirect_text" name="redirect_text"
                                      rows="3" cols="50" class="large-text"><?php echo esc_textarea($settings['redirect_text']); ?></textarea>
                            <p class="description">
                                <?php _e('النص التوضيحي الذي يظهر قبل زر التوجيه', 'ridcod-shorts'); ?>
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="button_text"><?php _e('نص زر التوجيه', 'ridcod-shorts'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="button_text" name="button_text"
                                   value="<?php echo esc_attr($settings['button_text']); ?>"
                                   class="regular-text" />
                            <p class="description">
                                <?php _e('النص الذي يظهر على زر التوجيه النهائي', 'ridcod-shorts'); ?>
                            </p>
                        </td>
                    </tr>
                </table>

                <div class="ridcod-buttons-group" style="display: flex; gap: 10px; align-items: center;">
                    <?php submit_button(__('حفظ إعدادات النصوص', 'ridcod-shorts'), 'primary', 'save_text_settings', false); ?>
                    <?php submit_button(__('إعادة تعيين للقيم الافتراضية', 'ridcod-shorts'), 'secondary', 'reset_text_settings', false, array('onclick' => 'return confirm("' . esc_js(__('هل أنت متأكد من إعادة تعيين جميع النصوص للقيم الافتراضية؟', 'ridcod-shorts')) . '");')); ?>
                </div>
            </form>
        </div>
    </div>

    <!-- تبويبة التصميم -->
                <div id="design" class="tab-content">
        <div class="ridcod-card">
            <h2><?php _e('تخصيص التصميم', 'ridcod-shorts'); ?></h2>
            <p><?php _e('يمكنك تخصيص ألوان صفحة الانتظار الوسيطة', 'ridcod-shorts'); ?></p>

            <form method="post" action="">
                <?php wp_nonce_field('ridcod_shorts_design_settings', 'ridcod_shorts_design_nonce'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="countdown_color"><?php _e('لون العداد التنازلي', 'ridcod-shorts'); ?></label>
                        </th>
                        <td>
                            <input type="color" id="countdown_color" name="countdown_color"
                                   value="<?php echo esc_attr($settings['countdown_color']); ?>" />
                            <p class="description">
                                <?php _e('لون النص والأرقام في العداد التنازلي', 'ridcod-shorts'); ?>
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="button_color"><?php _e('لون الأزرار', 'ridcod-shorts'); ?></label>
                        </th>
                        <td>
                            <input type="color" id="button_color" name="button_color"
                                   value="<?php echo esc_attr($settings['button_color']); ?>" />
                            <p class="description">
                                <?php _e('لون خلفية الأزرار', 'ridcod-shorts'); ?>
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="text_color"><?php _e('لون النصوص', 'ridcod-shorts'); ?></label>
                        </th>
                        <td>
                            <input type="color" id="text_color" name="text_color"
                                   value="<?php echo esc_attr($settings['text_color']); ?>" />
                            <p class="description">
                                <?php _e('لون النصوص العادية في الصفحة', 'ridcod-shorts'); ?>
                            </p>
                        </td>
                    </tr>
                </table>

                <div class="ridcod-buttons-group" style="display: flex; gap: 10px; align-items: center;">
                    <?php submit_button(__('حفظ إعدادات التصميم', 'ridcod-shorts'), 'primary', 'save_design_settings', false); ?>
                    <?php submit_button(__('إعادة تعيين للقيم الافتراضية', 'ridcod-shorts'), 'secondary', 'reset_design_settings', false, array('onclick' => 'return confirm("' . esc_js(__('هل أنت متأكد من إعادة تعيين جميع ألوان التصميم للقيم الافتراضية؟', 'ridcod-shorts')) . '");')); ?>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* تحسين التبويبات */
.nav-tab-wrapper {
    margin-bottom: 0;
    border-bottom: 1px solid #ccd0d4;
}

.nav-tab {
    background: #f1f1f1;
    border: 1px solid #ccd0d4;
    color: #555;
    font-weight: 600;
    text-decoration: none;
    padding: 12px 20px;
    margin-left: 5px;
    border-radius: 3px 3px 0 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.nav-tab:hover {
    background: #fff;
    color: #333;
    text-decoration: none;
}

.nav-tab-active {
    background: #fff !important;
    border-bottom: 1px solid #fff;
    color: #333 !important;
}

.tab-content {
    display: none;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-top: none;
    padding: 0;
    min-height: 400px;
}

.tab-content.active {
    display: block;
}

.ridcod-card {
    background: #fff;
    padding: 25px;
    margin: 0;
}

.ridcod-card h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.form-table th {
    width: 200px;
    font-weight: 600;
    color: #333;
}

.form-table td {
    padding: 15px 10px;
}

.form-table td p.description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
}

input[type="color"] {
    width: 60px;
    height: 40px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.large-text {
    width: 100%;
    max-width: 500px;
}

/* تحسين الأزرار */
.button-primary {
    background: linear-gradient(135deg, #0073aa, #005a87);
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.button-primary:hover {
    background: linear-gradient(135deg, #005a87, #004a73);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 115, 170, 0.3);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .ridcod-card {
        padding: 15px;
    }

    .form-table th,
    .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }

    .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }

    .nav-tab {
        margin-bottom: 5px;
        margin-left: 0;
        border-radius: 3px;
    }
}

/* تنسيق مجموعة الأزرار */
.ridcod-buttons-group {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

.ridcod-buttons-group .button {
    margin-right: 10px;
}

.ridcod-buttons-group .button-secondary {
    background: #f7f7f7;
    border-color: #cccccc;
    color: #555;
}

.ridcod-buttons-group .button-secondary:hover {
    background: #fafafa;
    border-color: #999;
    color: #23282d;
}

.ridcod-buttons-group .button-secondary:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 1px #007cba;
}

/* تحسين العرض على الأجهزة المحمولة */
@media (max-width: 768px) {
    .ridcod-buttons-group {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .ridcod-buttons-group .button {
        margin-right: 0;
        width: 100%;
        text-align: center;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // انتظار تحميل جميع الملفات
    $(window).on('load', function() {
        initSettingsTabs();
    });

    // تهيئة التبويبات فوراً أيضاً
    initSettingsTabs();

    function initSettingsTabs() {
        // إزالة أي أحداث سابقة لتجنب التكرار
        $('.nav-tab').off('click.settings');

        // التنقل بين التبويبات في صفحة الإعدادات
        $('.nav-tab').on('click.settings', function(e) {
            e.preventDefault();

            console.log('Settings tab clicked:', $(this).data('tab'));

            // إزالة الفئة النشطة من جميع التبويبات
            $('.nav-tab').removeClass('nav-tab-active');
            $('.tab-content').removeClass('active');

            // إضافة الفئة النشطة للتبويبة المحددة
            $(this).addClass('nav-tab-active');

            // الحصول على معرف التبويبة المستهدفة
            var targetTab = $(this).data('tab');
            $('#' + targetTab).addClass('active');

            // تحديث الرابط
            if (history.pushState) {
                history.pushState(null, null, '#' + targetTab);
            } else {
                window.location.hash = targetTab;
            }

            console.log('Active tab set to:', targetTab);
        });

        // التحقق من وجود hash في الرابط لفتح التبويبة المناسبة
        var hash = window.location.hash.substring(1);
        if (hash && $('#' + hash).length) {
            $('.nav-tab').removeClass('nav-tab-active');
            $('.tab-content').removeClass('active');
            $('[data-tab="' + hash + '"]').addClass('nav-tab-active');
            $('#' + hash).addClass('active');
            console.log('Hash tab activated:', hash);
        }

        // تحسين إمكانية الوصول
        $('.nav-tab').off('keydown.settings').on('keydown.settings', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                $(this).trigger('click.settings');
            }
        });

        console.log('Settings tabs initialized');
    }

    // تأثيرات لطيفة عند تحميل الصفحة
    $('.ridcod-card').each(function(index) {
        $(this).css('opacity', '0').delay(index * 100).animate({
            opacity: 1
        }, 500);
    });
});
</script>




